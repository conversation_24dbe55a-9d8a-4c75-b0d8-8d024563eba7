{"name": "debot-solana-parser", "version": "1.0.0", "description": "A TypeScript-based Solana DEX monitoring tool with a pluggable parser architecture.", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts"}, "keywords": ["solana", "typescript", "geyser", "dex"], "author": "", "license": "ISC", "dependencies": {"@coral-xyz/anchor": "^0.29.0", "@coral-xyz/borsh": "^0.29.0", "@grpc/grpc-js": "^1.9.13", "@grpc/proto-loader": "^0.7.10", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.91.1", "@triton-one/yellowstone-grpc": "^4.0.2", "@types/amqplib": "^0.10.7", "@types/ws": "^8.18.1", "amqplib": "^0.10.8", "borsh": "^1.0.0", "bs58": "^5.0.0", "decimal.js": "^10.4.3", "dotenv": "^16.3.1", "eventemitter3": "^5.0.1", "kafkajs": "^2.2.4", "ws": "^8.18.3"}, "devDependencies": {"@types/node": "^20.11.5", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}