# Kafka
KAFKA_BROKERS="************:19092"

# Geyser gRPC连接配置
GEYSER_GRPC_ADDRESS="https://downtowns-arcadian-kbfxejmomd-dedicated-lb.helius-rpc.com:2053"
GEYSER_TOKEN=5a1f74aa-cd01-478b-a7ca-590f252f79da

#"rabbitMq": {
#    "userName": "root",
#    "password": "MJXqlYfZ",
#    "host": "************",
#    "port": 5675,
#    "virtualHost": "/"
#  },

# RabbitMQ连接配置（推送测试需要）
AMQP_URL=amqp://root:MJXqlYfZ@************:5675

# Solana RPC配置（可选，默认使用主网）
SOLANA_RPC_URL="https://mainnet.helius-rpc.com/?api-key=5880e947-1482-47dc-943e-018d0e0434ac"

# 日志级别（可选）
LOG_LEVEL=info
