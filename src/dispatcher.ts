import bs58 from 'bs58';
import EventEmitter from 'eventemitter3';
import { IParser, SolanaDexNewPool, SolanaDexTrade } from './types';
import { EventAdapter } from './parsers/eventAdapter';
import {Program} from "@coral-xyz/anchor";
import {PublicKey} from "@solana/web3.js";

// 来自 Geyser proto 的交易更新的简化类型
type TransactionUpdate = any;

/**
 * Dispatcher (调度器) 是可插拔架构的核心。
 */
export class Dispatcher extends EventEmitter {
    private parserRegistry: Map<string, IParser> = new Map();

    /**
     * 为特定的程序地址注册一个解析器。
     * @param programId - DEX 的程序地址。
     * @param parser - 一个实现了 IParser 接口的类的实例。
     */
    public register(programId: string, parser: IParser) {
        if (this.parserRegistry.has(programId)) {
            console.warn(`程序ID ${programId} 的解析器已被注册。即将覆盖。`);
        }
        this.parserRegistry.set(programId, parser);
        const PROGRAM_ID = new PublicKey(
            programId
        );
        if (parser instanceof EventAdapter) {
            parser.onAsyncResults = (trades: SolanaDexTrade[], newPools: SolanaDexNewPool[]) => {
                if (trades.length > 0) {
                    this.emit('trade', trades);
                }
                if (newPools.length > 0) {
                    this.emit('new_pool', newPools);
                }
            };
        }
        
    }


    /**
     * 接收一笔交易并将其分发给适当的解析器。
     * @param txUpdate - 来自 Geyser 数据流的原始交易更新。
     */
    public dispatch(txUpdate: TransactionUpdate) {
        if (!txUpdate.transaction || !txUpdate.transaction.transaction) {
            return; // 不是一个有效的交易更新
        }

        const { transaction } = txUpdate.transaction;
        
        // 检查数据格式，适配Yellowstone格式
        // 修正：使用正确的Yellowstone数据结构路径
        const rawAccountKeys = transaction.transaction?.message?.accountKeys;
        if (!rawAccountKeys) {
            return;
        }
        
        // 将账户公钥转换为 Base58 字符串以便查找
        const accountKeys = rawAccountKeys.map((key: Buffer) => bs58.encode(key));

        const seenParsers = new Set<IParser>();

        // 检查顶层指令和内部指令 - 修正数据结构路径
        const allInstructions = [
            ...transaction.transaction.message.instructions,
            ...(transaction.meta?.innerInstructions || []).flatMap((ix: any) => ix.instructions)
        ];

        for (const instruction of allInstructions) {
            const programIdIndex = instruction.programIdIndex;
            const programId = accountKeys[programIdIndex];
            const parser = this.parserRegistry.get(programId);
            // 如果找到解析器且本次交易尚未调用过此解析器
            if (parser && !seenParsers.has(parser)) {
                const parsedData = parser.parse(txUpdate.transaction);
                if (parsedData) {
                    const trades = parsedData.filter(d => 'txType' in d) as SolanaDexTrade[];
                    const newPools = parsedData.filter(d => 'pairAddress' in d) as SolanaDexNewPool[];

                    if (trades.length > 0) {
                        this.emit('trade', trades);
                    }
                    if (newPools.length > 0) {
                        this.emit('new_pool', newPools);
                    }
                }
                seenParsers.add(parser);
            }
        }
    }
}
