import * as anchor from '@coral-xyz/anchor';
import {BN, EventParser} from '@coral-xyz/anchor';
import {Connection, PublicKey} from '@solana/web3.js';
import {
    CompleteTransactionData,
    ConfirmedTransaction,
    EventData,
    IAdvancedParser,
    InstructionData,
    ProcessingResult,
    PublishResult,
    SolanaDexNewPool,
    SolanaDexTrade,
    StandardizedData,
} from '../../types';
import {idlConfigManager} from '../idl/idlConfigManager';
import {MessageQueueConfig, MessageQueueService} from '../../services/messageQueueService';
import bs58 from 'bs58';

// 添加Pool数据接口定义
export interface MeteoraPoolData {
    address: string;
    lpMint: string;
    tokenAMint: string;
    tokenBMint: string;
    aVault: string;
    bVault: string;
    aVaultLp: string;
    bVaultLp: string;
    enabled: boolean;
    poolType: any;
    fees: {
        tradeFeeNumerator: string;
        tradeFeeDenominator: string;
        protocolTradeFeeNumerator: string;
        protocolTradeFeeDenominator: string;
    };
    protocolTokenAFee: string;
    protocolTokenBFee: string;
    curveType: any;
    totalLockedLp: string;
    bootstrapping?: {
        activationPoint: string;
        whitelistedVault: string;
        poolCreator: string;
        activationType: number;
    };
    partnerInfo?: {
        feeNumerator: string;
        partnerAuthority: string;
        pendingFeeA: string;
        pendingFeeB: string;
    };
    feeLastUpdatedAt: string;
    stake: string;
}

/**
 * 利用 IDL 的所有数据源：instructions、accounts、events、types、errors
 * 完全基于IDL动态解析，无硬编码指令索引，整合消息队列投递
 * 实现了IAdvancedParser接口，可作为其他DEX解析器的参考实现
 */
export class ComprehensiveMeteoraDataExtractor implements IAdvancedParser {
    private readonly supportedPrograms: string[];
    private programCache = new Map<string, anchor.Program>();
    private readonly connection: Connection;
    private messageQueue: MessageQueueService;


    constructor(
        supportedPrograms: string[],
        messageQueueConfig: MessageQueueConfig,
        rpcUrl?: string
    ) {
        this.supportedPrograms = supportedPrograms;
        this.connection = new Connection(rpcUrl || 'https://api.mainnet-beta.solana.com');
        this.messageQueue = new MessageQueueService(messageQueueConfig);

        console.log(`初始化完成，支持 ${supportedPrograms.length} 个程序`);
    }

    /**
     * 新增：直接使用IDL解析Pool账户数据
     */
    async parsePoolData(poolAddress: string | PublicKey, programId?: string): Promise<MeteoraPoolData | null> {
        try {
            const poolPubkey = typeof poolAddress === 'string'
                ? new PublicKey(poolAddress)
                : poolAddress;

            // 如果没有提供programId，则尝试所有支持的程序
            const programIds = programId ? [programId] : this.supportedPrograms;

            for (const programIdStr of programIds) {
                try {
                    const program = await this.getProgram(programIdStr);
                    if (!program) continue;

                    // 直接使用程序的account方法解析pool数据
                    const poolData = await program.account.pool.fetch(poolPubkey) as any;
                    // 解析得到的数据结构完全对应IDL中的pool定义
                    return {
                        // 基础信息
                        address: poolPubkey.toBase58(),
                        lpMint: poolData.lpMint?.toBase58() || '',
                        tokenAMint: poolData.tokenAMint?.toBase58() || '',
                        tokenBMint: poolData.tokenBMint?.toBase58() || '',

                        // Vault信息
                        aVault: poolData.aVault?.toBase58() || '',
                        bVault: poolData.bVault?.toBase58() || '',
                        aVaultLp: poolData.aVaultLp?.toBase58() || '',
                        bVaultLp: poolData.bVaultLp?.toBase58() || '',

                        // 状态信息
                        enabled: Boolean(poolData.enabled),
                        poolType: poolData.poolType,

                        // 费用信息
                        fees: {
                            tradeFeeNumerator: poolData.fees?.tradeFeeNumerator?.toString() || '0',
                            tradeFeeDenominator: poolData.fees?.tradeFeeDenominator?.toString() || '0',
                            protocolTradeFeeNumerator: poolData.fees?.protocolTradeFeeNumerator?.toString() || '0',
                            protocolTradeFeeDenominator: poolData.fees?.protocolTradeFeeDenominator?.toString() || '0'
                        },

                        // 协议费用账户
                        protocolTokenAFee: poolData.protocolTokenAFee?.toBase58() || '',
                        protocolTokenBFee: poolData.protocolTokenBFee?.toBase58() || '',

                        // 曲线类型（重要：决定了这是什么类型的池子）
                        curveType: poolData.curveType,

                        // 锁定LP信息
                        totalLockedLp: poolData.totalLockedLp?.toString() || '0',

                        // 启动配置
                        bootstrapping: poolData.bootstrapping ? {
                            activationPoint: poolData.bootstrapping.activationPoint?.toString() || '0',
                            whitelistedVault: poolData.bootstrapping.whitelistedVault?.toBase58() || '',
                            poolCreator: poolData.bootstrapping.poolCreator?.toBase58() || '',
                            activationType: poolData.bootstrapping.activationType || 0
                        } : undefined,

                        // 合作伙伴信息
                        partnerInfo: poolData.partnerInfo ? {
                            feeNumerator: poolData.partnerInfo.feeNumerator?.toString() || '0',
                            partnerAuthority: poolData.partnerInfo.partnerAuthority?.toBase58() || '',
                            pendingFeeA: poolData.partnerInfo.pendingFeeA?.toString() || '0',
                            pendingFeeB: poolData.partnerInfo.pendingFeeB?.toString() || '0'
                        } : undefined,

                        // 其他字段
                        feeLastUpdatedAt: poolData.feeLastUpdatedAt?.toString() || '0',
                        stake: poolData.stake?.toBase58() || ''
                    };

                } catch (err){
                    console.log(err);
                }
            }

            console.warn(`无法使用任何程序解析Pool: ${poolPubkey.toBase58()}`);
            return null;

        } catch (error) {
            console.error('解析Pool数据失败:', error);
            return null;
        }
    }



    /**
     * 新增：从交易中提取并解析Pool数据
     */
    async extractPoolsFromTransaction(transaction: ConfirmedTransaction): Promise<MeteoraPoolData[]> {
        const pools: MeteoraPoolData[] = [];
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];

        // 遍历交易中的所有账户，尝试解析为Pool
        for (const accountKey of accountKeys) {
            try {
                const pubkey = new PublicKey(accountKey)

                const poolData = await this.parsePoolData(pubkey);
                if (poolData) {
                    pools.push(poolData);
                }
            } catch {
                // 忽略无效的公钥或非Pool账户
            }
        }

        return pools;
    }


    /**
     * 实现IParser接口 - 兼容现有系统
     */
    parse(transaction: ConfirmedTransaction): (SolanaDexTrade | SolanaDexNewPool)[] | null {
        // 同步版本，启动异步处理但立即返回
        this.processTransaction(transaction).catch(error => {
            console.error('异步处理失败:', error);
        });
        return []; // 返回空数组，实际结果通过异步处理
    }

    /**
     * 实现IAdvancedParser.processTransaction
     */
    async processTransaction(transaction: ConfirmedTransaction): Promise<ProcessingResult> {
        const startTime = Date.now();

        try {
            // 如果交易有错误，则跳过处理
            if (transaction.transaction.meta.err) {
                return {
                    success: false,
                    processingTime: Date.now() - startTime,
                    tradesCount: 0,
                    poolsCount: 0,
                    extractedData: null,
                    standardizedData: null,
                    error: '交易执行失败，跳过处理'
                };
            }
            // 1. 完整数据提取（基于IDL）
            const extractedData = await this.extractCompleteData(transaction);
            // 2. 转换为标准格式
            const standardizedData = await this.standardizeData(extractedData, transaction);

            // 3. 投递到消息队列
            const publishResult = await this.publishData(standardizedData);


            return {
                success: true,
                processingTime: Date.now() - startTime,
                tradesCount: publishResult.tradesPublished,
                poolsCount: publishResult.poolsPublished,
                extractedData,
                standardizedData,
                error: null
            };
        } catch (error: any) {
            console.error('处理交易失败:', error);

            return {
                success: false,
                processingTime: Date.now() - startTime,
                tradesCount: 0,
                poolsCount: 0,
                extractedData: null,
                standardizedData: null,
                error: error.message
            };
        }
    }

    /**
     * 数据解析
     */
    private async extractCompleteData(transaction: ConfirmedTransaction): Promise<CompleteTransactionData> {
        const result: CompleteTransactionData = {
            events: [],
            instructions: [],
            accountStates: [],
            metadata: {
                txHash: bs58.encode(transaction.transaction.signature),
                slot: transaction.slot || 0,
                timestamp: Math.floor(Date.now() / 1000)
            }
        };
        // 1. 提取事件数据
        result.events = await this.extractEventData(transaction);
        // 2. 提取指令数据
        result.instructions = await this.extractInstructionData(transaction);
        // 3. 提取账户状态数据
        // result.accountStates = await this.extractProgramAccountStates(transaction);
        // // 4.提取Pool数据
        // const poolsData = await this.extractPoolsFromTransaction(transaction);
        return result;
    }

    /**
     * 1. 事件数据提取（基于IDL事件定义）
     */
    private async extractEventData(transaction: ConfirmedTransaction): Promise<EventData[]> {
        const events: EventData[] = [];

        for (const programId of this.supportedPrograms) {
            const program = await this.getProgram(programId);
            if (!program) continue;

            // 基于IDL解析事件
            const programEvents = await this.parseEventsForProgram(transaction, program, programId);
            events.push(...programEvents);
        }
        return events;
    }

    /**
     * 2. 指令数据提取（处理顶层指令和内部指令）
     */
    private async extractInstructionData(transaction: ConfirmedTransaction): Promise<InstructionData[]> {
        const instructions: InstructionData[] = [];

        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        console.log("transaction:", transaction);
        // 1. 处理顶层指令
        const topLevelInstructions = transaction.transaction?.transaction?.message?.instructions || [];
        console.log("处理顶层指令，数量:", topLevelInstructions.length);
        for (let i = 0; i < topLevelInstructions.length; i++) {
             const instruction = topLevelInstructions[i];
             console.log("顶层指令", i, "programIdIndex:", instruction.programIdIndex);
             console.log("顶层指令", i, "accountKeys[programIdIndex]:", accountKeys[instruction.programIdIndex]);
             console.log("顶层指令", i, "typeof:", typeof accountKeys[instruction.programIdIndex]);
             
             const programId = bs58.encode(accountKeys[instruction.programIdIndex]);

             await this.processInstruction(instruction, programId, accountKeys, i, `top_${i}`, instructions);
         }

        // 2. 处理内部指令 - 这里往往包含实际的DEX操作
        const innerInstructions = transaction.transaction.meta?.innerInstructions || [];
        for (let outerIndex = 0; outerIndex < innerInstructions.length; outerIndex++) {
            const innerInstructionGroup = innerInstructions[outerIndex];
            const innerInstructionList = innerInstructionGroup.instructions || [];

            for (let innerIndex = 0; innerIndex < innerInstructionList.length; innerIndex++) {
                const instruction = innerInstructionList[innerIndex];
                console.log("instruction.programIdIndex:", instruction.programIdIndex);
                console.log("accountKeys.length:", accountKeys.length);
                console.log("accountKeys[instruction.programIdIndex]:", accountKeys[instruction.programIdIndex]);
                console.log("typeof accountKeys[instruction.programIdIndex]:", typeof accountKeys[instruction.programIdIndex]);
                
                // 检查索引是否有效
                if (instruction.programIdIndex >= accountKeys.length) {
                    console.warn(`programIdIndex ${instruction.programIdIndex} 超出 accountKeys 长度 ${accountKeys.length}`);
                    continue;
                }
                
                const accountKey = accountKeys[instruction.programIdIndex];
                if (accountKey === undefined || accountKey === null) {
                    console.warn(`accountKeys[${instruction.programIdIndex}] 是 undefined 或 null`);
                    continue;
                }
                
                const programId = bs58.encode(accountKeys[instruction.programIdIndex]);

                await this.processInstruction(
                     instruction,
                     programId,
                     accountKeys,
                     innerIndex, // 使用内部指令的索引作为数字索引
                     `inner_${outerIndex}_${innerIndex}`,
                     instructions
                 );
            }
        }
        return instructions;
    }


    /**
     * 处理单个指令（顶层或内部指令）
     */
    private async processInstruction(
         instruction: any,
         programId: string,
         accountKeys: any[],
         index: number,
         instructionId: string,
         instructions: InstructionData[]
     ): Promise<void> {
        if (this.supportedPrograms.includes(programId)) {
            const program = await this.getProgram(programId);
            if (!program) return;


            try {
                const instructionBuffer = Buffer.from(instruction.data);
                const decodedInstruction = this.decodeInstructionData(instructionBuffer, program);
                if (!decodedInstruction) {
                    return;
                }

                const instructionDef = program.idl.instructions.find(
                    (inst: any) => inst.name === decodedInstruction.name
                );
                if (instructionDef) {
                    const instructionData = await this.buildInstructionData(
                         decodedInstruction,
                         instructionDef,
                         instruction,
                         accountKeys,
                         programId,
                         index,
                         instructionId
                     );

                    instructions.push(instructionData);
                }
            } catch (error) {
                console.warn(`解析指令 ${instructionId} 失败:`, error);
            }
        }
    }

    /**
     * 解码指令数据
     */
    private decodeInstructionData(data: Buffer, program: anchor.Program): any {
        try {

            if (data.length === 0) {
                console.log("指令数据为空");
                return null;
            }

            // 使用 Anchor 的 BorshInstructionCoder 解码指令
            const coder = new anchor.BorshInstructionCoder(program.idl);
            const decoded = coder.decode(data);
            if (decoded) {

                // 获取指令定义
                const instructionDef = program.idl.instructions.find(
                    (inst: any) => inst.name === decoded.name
                );

                return {
                    name: decoded.name,
                    data: decoded.data,
                    definition: instructionDef
                };
            }
            return null;

        } catch (error) {
            return null;
        }
    }
    /**
     * 转换为标准格式（基于提取的数据）
     */
    private async standardizeData(
        extractedData: CompleteTransactionData,
        transaction: ConfirmedTransaction
    ): Promise<StandardizedData> {
        const trades: SolanaDexTrade[] = [];
        const pools: SolanaDexNewPool[] = [];

        // 从指令数据转换
        for (const [key,instruction] of Object.entries(extractedData.instructions)) {
            const eventIndex = parseInt(key);
            const event = eventIndex >= 0 && eventIndex < extractedData.events.length ? extractedData.events[eventIndex] : undefined;
            const standardized = await this.standardizeInstruction(instruction, extractedData, transaction, event);
            if (standardized) {
                if (standardized.type === 'trade') {
                    trades.push(standardized.data);
                } else if (standardized.type === 'pool') {
                    pools.push(standardized.data);
                }
            }
        }

        return { trades, pools };
    }

    /**
     * 标准化指令数据
     */
    private async standardizeInstruction(
        instruction: InstructionData,
        context: CompleteTransactionData,
        transaction: ConfirmedTransaction,
        event?: EventData
    ): Promise<{type: 'trade' | 'pool', data: any} | null> {
        // 基于指令名称和提取的数据创建标准格式
        const instructionName = instruction.name;
        try {
            if (instructionName === 'swap') {
                // 获取储备量数据
                const { token0Reserve, token1Reserve } = this.extractReserves(
                    instruction,
                    transaction
                );

                return {
                    type: 'trade',
                    data: {
                        dexName: 'meteora',
                        poolAddress: instruction.accounts.pool,
                        txHash: context.metadata.txHash,
                        txType: 'buy', // 可以基于数据推断
                        slot: context.metadata.slot,
                        instructionIndex: instruction.instructionId,
                        timestamp: context.metadata.timestamp,
                        traderAddress: instruction.accounts.userSourceToken,
                        token0Address: instruction.accounts.aVaultLpMint || instruction.accounts.aVaultLpMint || 'unknown',
                        token1Address: instruction.accounts.bVaultLpMint || instruction.accounts.bVaultLpMint || 'unknown',
                        token0Amount: event ? BN(event.data.inAmount).toString() : '0',
                        token1Amount: event ? BN(event.data.outAmount).toString() : '0',
                        token0Reserve,
                        token1Reserve,
                        dexSource: `meteora-test`,
                    } as SolanaDexTrade
                };
            }
        } catch (error) {
            console.warn('标准化指令失败:', error);
        }

        return null;
    }

    /**
     * 从交易中提取储备量数据
     */
    private extractReserves(
        instruction: InstructionData,
        transaction: ConfirmedTransaction
    ): { token0Reserve: string, token1Reserve: string } {
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        const postTokenBalances = transaction.transaction.meta?.postTokenBalances || [];
        const postBalances = transaction.transaction.meta?.postBalances || [];


        // 获取vault地址
        const aVault = instruction.accounts.aVaultLp;
        const bVault = instruction.accounts.bVaultLp;


        let token0Reserve = '0';
        let token1Reserve = '0';

        // 查找vault在accountKeys中的索引
        const aVaultIndex = accountKeys.findIndex((key: any) =>
            (typeof key === 'string' ? key : bs58.encode(key)) === aVault
        );
        const bVaultIndex = accountKeys.findIndex((key: any) =>
            (typeof key === 'string' ? key : bs58.encode(key)) === bVault
        );


        // 获取token0储备量 (aVault)
        if (aVaultIndex !== -1) {
            // 优先检查postTokenBalances中的代币余额
            const tokenBalance = postTokenBalances.find((tb: any) => tb.accountIndex === aVaultIndex);

            if (tokenBalance?.uiTokenAmount?.uiAmountString) {
                token0Reserve = tokenBalance.uiTokenAmount.uiAmountString;
            } else {
                // 如果没有代币余额，检查SOL余额
                if (aVaultIndex < postBalances.length) {
                    const balance = postBalances[aVaultIndex];
                    if (balance > 0) {
                        token0Reserve = (balance / 1e9).toString();
                    }
                }
            }
        }

        // 获取token1储备量 (bVault)
        if (bVaultIndex !== -1) {

            // 优先检查postTokenBalances中的代币余额
            const tokenBalance = postTokenBalances.find((tb: any) => tb.accountIndex === bVaultIndex);

            if (tokenBalance?.uiTokenAmount?.uiAmountString) {
                token1Reserve = tokenBalance.uiTokenAmount.uiAmountString;
            } else {
                // 如果没有代币余额，检查SOL余额
                if (bVaultIndex < postBalances.length) {
                    const balance = postBalances[bVaultIndex];
                    if (balance > 0) {
                        token1Reserve = (balance / 1e9).toString();
                    }
                }
            }
        }


        return { token0Reserve, token1Reserve };
    }

    /**
     * 投递数据到消息队列
     */
    private async publishData(data: StandardizedData): Promise<PublishResult> {
        let tradesPublished = 0;
        let poolsPublished = 0;

        try {
            // 发布交易数据
            for (const trade of data.trades) {
                await this.messageQueue.publishTrade(trade);
                tradesPublished++;
            }

            // 发布池子数据
            for (const pool of data.pools) {
                await this.messageQueue.publishNewPool(pool);
                poolsPublished++;
            }

            return { tradesPublished, poolsPublished };
        } catch (error) {
            console.error('发布数据失败:', error);
            throw error;
        }
    }

    /**
     * 构建指令数据（核心方法 - 无硬编码索引）
     */
    private async buildInstructionData(
        decodedInstruction: any,
        instructionDef: any,
        rawInstruction: any,
        accountKeys: string[],
        programId: string,
        index: number,
        instructionId: string
    ): Promise<InstructionData> {
        // 🔥 关键：自动映射账户（基于IDL定义，无硬编码）
        const accounts = this.mapInstructionAccounts(
            instructionDef.accounts,
            rawInstruction.accounts,
            accountKeys
        );

        return {
            name: decodedInstruction.name,
            programId,
            index,
            instructionId,
            data: decodedInstruction.data,
            accounts,
        };
    }

    /**
     * 自动映射指令账户（不需要硬编码索引）
     */
    private mapInstructionAccounts(
        accountDefs: any[],
        accountIndices: number[],
        accountKeys: string[]
    ): Record<string, string> {
        const accounts: Record<string, string> = {};

        accountDefs.forEach((accountDef, index) => {
            if (index < accountIndices.length) {
                const accountIndex = accountIndices[index];
                if (accountIndex < accountKeys.length) {
                    accounts[accountDef.name] = bs58.encode(accountKeys[accountIndex] as any);
                }
            }
        });

        return accounts;
    }

    /**
     * 获取程序实例
     */
    private async getProgram(programIdString: string): Promise<anchor.Program | null> {
        if (this.programCache.has(programIdString)) {
            return this.programCache.get(programIdString)!;
        }

        try {
            const config = idlConfigManager.getConfig(programIdString);
            if (!config) return null;

            // 🔥 修复：动态加载IDL
            const idlModule = await import(config.path);
            const idl = idlModule.IDL;

            const programId = new PublicKey(programIdString);

            // 🔥 修复：创建一个不需要钱包的 provider
            // 对于解析数据，我们不需要发送交易，所以创建一个最小的 provider
            const provider = new anchor.AnchorProvider(
                this.connection,
                // 创建一个虚拟钱包，只用于满足 provider 要求
                {
                    publicKey: new PublicKey('11111111111111111111111111111111'), // 系统程序公钥作为占位符
                } as any,
                { commitment: 'confirmed' }
            );

            const program = new anchor.Program(idl, programId, provider);
            this.programCache.set(programIdString, program);
            return program;
        } catch (error) {
            console.error(`创建程序实例失败 ${programIdString}:`, error);
            return null;
        }
    }

    private async parseEventsForProgram(
        transaction: ConfirmedTransaction,
        program: anchor.Program,
        programId: string
    ): Promise<EventData[]> {
        const events: EventData[] = [];
        try {
            // 检查必要的数据结构
            if (transaction.transaction.meta.err != undefined) {
                return events;
            }

            const logs = transaction.transaction.meta?.logMessages || [];
            if (logs.length === 0) return events;
            // 使用 Anchor 的 EventParser 自动解析日志
            const parser = new EventParser(new PublicKey(programId), program.coder);
            const parsedEvents = parser.parseLogs(logs);
            for (const event of parsedEvents) {
                events.push({
                    name: event.name,
                    programId,
                    data: event.data,
                });
            }
        } catch (error) {
            console.warn(`解析程序 ${programId} 的事件失败:`, error);
        }
        return events;
    }

    /**
     * 实现IAdvancedParser.initialize
     */
    async initialize(): Promise<void> {
        await this.messageQueue.initialize();
        console.log('初始化完成');
    }
}
