import fs from 'fs';
import path from 'path';

export interface IDLConfig {
    name: string;
    programId: string;
    programType: 'anchor' | 'native';
    path: string;
}

/**
 * IDL配置管理器
 */
export class IDLConfigManager {
    private configMap = new Map<string, IDLConfig>();

    constructor(configPath: string = './src/parsers/idl/configs') {
        this.loadConfigs(configPath);
    }

    /**
     * 加载配置文件
     */
    private loadConfigs(configPath: string): void {
        try {
            const configFile = path.join(configPath, 'dex-configs.json');
            
            if (fs.existsSync(configFile)) {
                const data = fs.readFileSync(configFile, 'utf-8');
                const configs: IDLConfig[] = JSON.parse(data);
                
                configs.forEach(config => {
                    this.configMap.set(config.programId, config);
                });
                
                console.log(`IDL配置加载了 ${configs.length} 个DEX`);
            }
        } catch (error) {
            console.error('IDL配置加载失败:', error);
        }
    }

    /**
     * 根据programId获取配置
     */
    getConfig(programId: string): IDLConfig | null {
        return this.configMap.get(programId) || null;
    }
}

export const idlConfigManager = new IDLConfigManager();