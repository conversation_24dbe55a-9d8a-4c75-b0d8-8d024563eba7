import { IParser, ConfirmedTransaction, SolanaDexTrade, SolanaDexNewPool } from '../types';
import { ComprehensiveMeteoraDataExtractor } from './meteora/comprehensiveDataExtractor';
import { METEORA_PROGRAM_IDS } from './meteora/constants';
import { MessageQueueConfig } from '../services/messageQueueService';
import bs58 from 'bs58';

/**
 * 现代化事件解析器适配器 - 基于ComprehensiveMeteoraDataExtractor
 * 保持IParser接口兼容，内部使用IDL驱动的完整解析器
 */
export class EventAdapter implements IParser {
    private extractor: ComprehensiveMeteoraDataExtractor;
    private initialized: boolean = false;
    
    // 异步结果回调函数 - 由 Dispatcher 设置
    public onAsyncResults?: (trades: SolanaDexTrade[], newPools: SolanaDexNewPool[]) => void;

    constructor(messageQueueConfig?: MessageQueueConfig) {
        // 如果没有提供消息队列配置，使用默认配置（禁用投递）
        const defaultConfig: MessageQueueConfig = {
            kafka: { enabled: false, clientId: 'event-adapter', brokers: [] },
            rabbitmq: { enabled: false, url: '' }
        };

        this.extractor = new ComprehensiveMeteoraDataExtractor(
            METEORA_PROGRAM_IDS,
            messageQueueConfig || defaultConfig
        );
    }

    /**
     * 确保提取器已初始化
     */
    private async ensureInitialized(): Promise<void> {
        if (!this.initialized) {
            await this.extractor.initialize();
            this.initialized = true;
        }
    }

    /**
     * 实现 IParser 接口的 parse 方法
     * 由于事件解析器是异步的，我们返回 null 并通过回调发送结果
     */
    parse(transaction: ConfirmedTransaction): (SolanaDexTrade | SolanaDexNewPool)[] | null {
        console.log(`收到解析请求`);
        
        if (!transaction) {
            console.log('交易数据为空');
            return null;
        }

        try {
            // 生成交易ID用于日志跟踪
            const txId = this.generateTxId(transaction);
            console.log(`处理交易: ${txId}`);

            // 启动异步IDL驱动解析
            this.parseAsync(txId, transaction);

            return [];

        } catch (error) {
            console.error('解析失败:', error);
            return null;
        }
    }

    /**
     * 异步解析事件（使用IDL驱动的完整提取器）
     */
    private async parseAsync(txId: string, transaction: ConfirmedTransaction): Promise<void> {
        try {
            // 确保初始化
            await this.ensureInitialized();

            console.log(`开始解析: ${txId}`);
            
            // 使用ComprehensiveMeteoraDataExtractor处理
            const result = await this.extractor.processTransaction(transaction);

            if (result.success && result.standardizedData) {
                const { trades, pools } = result.standardizedData;
                
                console.log(`${txId} 解析完成: ${trades.length} 交易, ${pools.length} 池子`);
                
                // 输出结果（保持原有接口行为）
                const allResults = [...trades, ...pools];
                if (allResults.length > 0) {
                    console.log(`输出 ${allResults.length} 个解析结果`);
                    
                    // 这里可以触发回调或事件，保持原有API行为
                    this.emitResults(txId, allResults);
                }
            } else {
                console.log(`${txId} 解析失败: ${result.error}`);
            }

        } catch (error) {
            console.error(`${txId} 异步解析失败:`, error);
        }
    }

    /**
     * 发出解析结果（保持原有API兼容性）
     */
    private emitResults(txId: string, results: (SolanaDexTrade | SolanaDexNewPool)[]): void {
        console.log(`发出解析结果 ${txId}:`, results.length);
        
        // 分离交易和池子数据
        const trades = results.filter(result => 'txType' in result) as SolanaDexTrade[];
        const newPools = results.filter(result => 'pairAddress' in result) as SolanaDexNewPool[];
        
        // 日志输出
        results.forEach((result, index) => {
            if ('txType' in result) {
                const trade = result as SolanaDexTrade;
                console.log(`交易 ${index + 1}: ${trade.txType} on ${trade.dexName}`);
            } else {
                const pool = result as SolanaDexNewPool;
                console.log(`池子 ${index + 1}: ${pool.dexName} 新池`);
            }
        });

        // 如果设置了异步回调，调用它来传递结果给 Dispatcher
        if (this.onAsyncResults && (trades.length > 0 || newPools.length > 0)) {
            console.log(`通过回调传递结果: ${trades.length} 交易, ${newPools.length} 池子`);
            this.onAsyncResults(trades, newPools);
        }
    }

    /**
     * 生成交易ID
     */
    private generateTxId(transaction: ConfirmedTransaction): string {
        try {
            const signatures = transaction.transaction?.transaction?.signatures;
            if (signatures && signatures.length > 0) {
                const sig = typeof signatures[0] === 'string' 
                    ? signatures[0] 
                    : bs58.encode(signatures[0]);
                return sig.substring(0, 8);
            }
        } catch (error) {
            console.warn('生成交易ID失败:', error);
        }
        return `tx_${Date.now().toString(36)}`;
    }

}
