import { <PERSON><PERSON><PERSON>, Producer } from 'kafkajs';
import * as amqp from 'amqplib';
import { SolanaDexTrade, SolanaDexNewPool } from '../types';

/**
 * 统一消息队列服务
 * 支持 Kafka 和 RabbitMQ 的数据投递
 */
export class MessageQueueService {
    private kafkaProducer?: Producer;
    private rabbitChannel?: amqp.Channel;
    private config: MessageQueueConfig;

    constructor(config: MessageQueueConfig) {
        this.config = config;
    }

    /**
     * 初始化连接
     */
    async initialize(): Promise<void> {
        if (this.config.kafka.enabled) {
            await this.initializeKafka();
        }

        if (this.config.rabbitmq.enabled) {
            await this.initializeRabbitMQ();
        }
    }

    /**
     * 投递交易数据
     */
    async publishTrade(trade: SolanaDexTrade): Promise<void> {
        const message = this.formatTradeMessage(trade);

        await Promise.all([
            this.publishToKafka('solana-dex-trades', message),
            this.publishToRabbitMQ('solana.dex.trades', message)
        ]);
    }

    /**
     * 投递新池数据
     */
    async publishNewPool(pool: SolanaDexNewPool): Promise<void> {
        const message = this.formatPoolMessage(pool);

        await Promise.all([
            this.publishToKafka('solana-dex-pools', message),
            this.publishToRabbitMQ('solana.dex.pools', message)
        ]);
    }

    /**
     * 批量投递数据
     */
    async publishBatch(trades: SolanaDexTrade[], pools: SolanaDexNewPool[]): Promise<void> {
        const batchMessage = {
            timestamp: Date.now(),
            trades: trades.map(trade => this.formatTradeMessage(trade)),
            pools: pools.map(pool => this.formatPoolMessage(pool)),
            metadata: {
                tradeCount: trades.length,
                poolCount: pools.length,
                source: 'meteora-parser'
            }
        };

        await Promise.all([
            this.publishToKafka('solana-dex-batch', batchMessage),
            this.publishToRabbitMQ('solana.dex.batch', batchMessage)
        ]);
    }

    // ==================== Kafka 相关方法 ====================

    private async initializeKafka(): Promise<void> {
        try {
            const kafka = new Kafka({
                clientId: this.config.kafka.clientId,
                brokers: this.config.kafka.brokers,
                retry: {
                    initialRetryTime: 100,
                    retries: 8
                }
            });

            this.kafkaProducer = kafka.producer({
                maxInFlightRequests: 1,
                idempotent: true,
                transactionTimeout: 30000,
            });

            await this.kafkaProducer.connect();
            console.log('Kafka 连接成功');
        } catch (error) {
            console.error('Kafka 连接失败:', error);
        }
    }

    private async publishToKafka(topic: string, message: any): Promise<void> {
        if (!this.kafkaProducer) return;

        try {
            await this.kafkaProducer.send({
                topic,
                messages: [{
                    key: this.generateMessageKey(message),
                    value: JSON.stringify(message),
                    timestamp: Date.now().toString(),
                    headers: {
                        'content-type': 'application/json',
                        'source': 'meteora-parser',
                        'version': '1.0'
                    }
                }]
            });

            console.log(`Kafka 消息发送成功: ${topic}`);
        } catch (error) {
            console.error(`Kafka 消息发送失败 (${topic}):`, error);
        }
    }

    // ==================== RabbitMQ 相关方法 ====================

    private async initializeRabbitMQ(): Promise<void> {
        try {
            const connection = await amqp.connect(this.config.rabbitmq.url);
            this.rabbitChannel = await connection.createChannel();

            // 声明交换机
            await this.rabbitChannel.assertExchange('solana-dex', 'topic', {
                durable: true
            });

            // 声明队列
            await this.rabbitChannel.assertQueue('solana-dex-trades', { durable: true });
            await this.rabbitChannel.assertQueue('solana-dex-pools', { durable: true });
            await this.rabbitChannel.assertQueue('solana-dex-batch', { durable: true });

            console.log('RabbitMQ 连接成功');
        } catch (error) {
            console.error('RabbitMQ 连接失败:', error);
        }
    }

    private async publishToRabbitMQ(routingKey: string, message: any): Promise<void> {
        if (!this.rabbitChannel) return;

        try {
            const messageBuffer = Buffer.from(JSON.stringify(message));

            this.rabbitChannel.publish(
                'solana-dex',
                routingKey,
                messageBuffer,
                {
                    persistent: true,
                    timestamp: Date.now(),
                    headers: {
                        'content-type': 'application/json',
                        'source': 'meteora-parser',
                        'version': '1.0'
                    }
                }
            );

            console.log(`RabbitMQ 消息发送成功: ${routingKey}`);
        } catch (error) {
            console.error(`RabbitMQ 消息发送失败 (${routingKey}):`, error);
        }
    }

    // ==================== 消息格式化方法 ====================

    private formatTradeMessage(trade: SolanaDexTrade): FormattedTradeMessage {
        return {
            // 基础信息
            id: `${trade.txHash}-${trade.instructionIndex}`,
            txHash: trade.txHash,
            slot: trade.slot,
            timestamp: trade.timestamp,
            
            // 协议信息
            protocol: 'meteora',
            dexName: trade.dexName,
            dexSource: trade.dexSource,
            
            // 交易信息
            txType: trade.txType,
            traderAddress: trade.traderAddress,
            
            // 池子信息
            poolAddress: trade.poolAddress,
            token0Address: trade.token0Address,
            token1Address: trade.token1Address,
            
            // 数量信息
            token0Amount: trade.token0Amount,
            token1Amount: trade.token1Amount,
            token0Reserve: trade.token0Reserve,
            token1Reserve: trade.token1Reserve,
            
            // 计算字段
            price: this.calculatePrice(trade.token0Amount, trade.token1Amount),
            volumeUSD: this.calculateVolumeUSD(trade),
            
            // 元数据
            metadata: {
                instructionIndex: trade.instructionIndex,
                parseSource: 'meteora-event-parser',
                parseTimestamp: Date.now()
            }
        };
    }

    private formatPoolMessage(pool: SolanaDexNewPool): FormattedPoolMessage {
        return {
            // 基础信息
            id: `${pool.creationTxHash}-${pool.pairAddress}`,
            txHash: pool.creationTxHash,
            blockNumber: pool.blockNumber,
            timestamp: pool.timestamp,
            
            // 协议信息
            protocol: 'meteora',
            dexName: pool.dexName,
            
            // 池子信息
            pairAddress: pool.pairAddress,
            token0Address: pool.token0Address,
            token1Address: pool.token1Address,
            
            // 元数据
            metadata: {
                parseSource: 'meteora-event-parser',
                parseTimestamp: Date.now()
            }
        };
    }

    // ==================== 辅助方法 ====================

    private generateMessageKey(message: any): string {
        if (message.txHash && message.instructionIndex) {
            return `${message.txHash}-${message.instructionIndex}`;
        } else if (message.txHash) {
            return message.txHash;
        } else {
            return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }
    }

    private calculatePrice(token0Amount: string, token1Amount: string): string {
        try {
            const amount0 = parseFloat(token0Amount);
            const amount1 = parseFloat(token1Amount);
            
            if (amount0 === 0) return '0';
            
            return (amount1 / amount0).toFixed(8);
        } catch (error) {
            return '0';
        }
    }

    private calculateVolumeUSD(trade: SolanaDexTrade): string {
        // TODO: 集成价格预言机获取USD价格
        return '0';
    }

    /**
     * 关闭连接
     */
    async close(): Promise<void> {
        if (this.kafkaProducer) {
            await this.kafkaProducer.disconnect();
        }

        if (this.rabbitChannel) {
            await this.rabbitChannel.close();
        }
    }
}

// ==================== 类型定义 ====================

export interface MessageQueueConfig {
    kafka: {
        enabled: boolean;
        clientId: string;
        brokers: string[];
    };
    rabbitmq: {
        enabled: boolean;
        url: string;
    };
}

export interface FormattedTradeMessage {
    id: string;
    txHash: string;
    slot: number;
    timestamp: number;
    protocol: string;
    dexName: string;
    dexSource: string;
    txType: string;
    traderAddress: string;
    poolAddress: string;
    token0Address: string;
    token1Address: string;
    token0Amount: string;
    token1Amount: string;
    token0Reserve: string;
    token1Reserve: string;
    price: string;
    volumeUSD: string;
    metadata: {
        instructionIndex: string;
        parseSource: string;
        parseTimestamp: number;
    };
}

export interface FormattedPoolMessage {
    id: string;
    txHash: string;
    blockNumber: number;
    timestamp: number;
    protocol: string;
    dexName: string;
    pairAddress: string;
    token0Address: string;
    token1Address: string;
    metadata: {
        parseSource: string;
        parseTimestamp: number;
    };
} 